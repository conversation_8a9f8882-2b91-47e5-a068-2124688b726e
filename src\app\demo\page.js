'use client';

import { useState, useEffect, useRef } from 'react';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';

export default function DemoPage() {
  const [currentLevel, setCurrentLevel] = useState(1);
  const [demoState, setDemoState] = useState('idle'); // idle, posting, receiving, completed
  const [offers, setOffers] = useState([]);
  const [userRequest, setUserRequest] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('electronics');
  const [budget, setBudget] = useState('');
  const [location, setLocation] = useState('');
  const [showChat, setShowChat] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [messages, setMessages] = useState([]);

  const demoRef = useRef(null);

  // Demo levels configuration
  const demoLevels = {
    1: {
      title: "Basic Demo",
      description: "Simple product request with basic offers",
      duration: "30-60 seconds",
      scenario: "iPhone 15 Pro",
      offerCount: 5
    },
    2: {
      title: "Enhanced Demo", 
      description: "Complex request with detailed offers and negotiation",
      duration: "2-3 minutes",
      scenario: "Gaming laptop under $1500",
      offerCount: 10
    },
    3: {
      title: "Advanced Demo",
      description: "Service request with portfolios and full simulation",
      duration: "5+ minutes", 
      scenario: "Wedding photography package",
      offerCount: 15
    }
  };

  const categories = [
    { id: 'electronics', name: 'Electronics', icon: '📱' },
    { id: 'fashion', name: 'Fashion', icon: '👕' },
    { id: 'home', name: 'Home & Garden', icon: '🏠' },
    { id: 'automotive', name: 'Automotive', icon: '🚗' },
    { id: 'services', name: 'Services', icon: '🛠️' },
    { id: 'sports', name: 'Sports & Outdoors', icon: '⚽' }
  ];

  // Initialize demo animations
  useEffect(() => {
    const initAnimations = async () => {
      if (typeof window !== 'undefined') {
        const { gsap } = await import('gsap');
        
        gsap.from('.demo-header', { 
          duration: 1, 
          y: 30, 
          opacity: 0, 
          ease: 'power3.out' 
        });
        
        gsap.from('.demo-level-card', { 
          duration: 0.8, 
          y: 20, 
          opacity: 0, 
          stagger: 0.2,
          ease: 'power3.out' 
        });
      }
    };

    initAnimations();
  }, []);

  // Simulate receiving offers
  const simulateOffers = async () => {
    setDemoState('posting');
    setOffers([]);
    
    // Simulate posting delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    setDemoState('receiving');

    const level = demoLevels[currentLevel];
    const offerTemplates = generateOfferTemplates(level.scenario, level.offerCount);
    
    // Simulate offers coming in over time
    for (let i = 0; i < offerTemplates.length; i++) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
      setOffers(prev => [...prev, offerTemplates[i]]);
    }
    
    setDemoState('completed');
  };

  // Generate realistic offers based on scenario
  const generateOfferTemplates = (scenario, count) => {
    const baseOffers = {
      'iPhone 15 Pro': [
        { seller: 'TechHub Store', price: 999, rating: 4.8, location: 'Local', verified: true, description: 'Brand new, sealed box with warranty' },
        { seller: 'Mobile World', price: 1049, rating: 4.6, location: 'Online', verified: true, description: 'Free shipping, 30-day return' },
        { seller: 'Electronics Plus', price: 979, rating: 4.9, location: 'Local', verified: true, description: 'Price match guarantee' },
        { seller: 'Digital Deals', price: 1029, rating: 4.5, location: 'Online', verified: false, description: 'Refurbished, like new condition' },
        { seller: 'Smart Devices Co', price: 1099, rating: 4.7, location: 'Local', verified: true, description: 'Bundle with accessories' }
      ],
      'Gaming laptop under $1500': [
        { seller: 'Gaming Central', price: 1299, rating: 4.9, location: 'Local', verified: true, description: 'RTX 4060, 16GB RAM, 1TB SSD' },
        { seller: 'PC Paradise', price: 1399, rating: 4.7, location: 'Online', verified: true, description: 'RTX 4070, 32GB RAM, 2TB SSD' },
        { seller: 'Tech Solutions', price: 1199, rating: 4.6, location: 'Local', verified: true, description: 'RTX 4050, 16GB RAM, 512GB SSD' },
        { seller: 'Laptop Experts', price: 1449, rating: 4.8, location: 'Online', verified: true, description: 'RTX 4070, 16GB RAM, 1TB SSD' },
        { seller: 'Digital World', price: 1349, rating: 4.5, location: 'Local', verified: false, description: 'RTX 4060, 32GB RAM, 1TB SSD' }
      ],
      'Wedding photography package': [
        { seller: 'Eternal Moments', price: 2500, rating: 4.9, location: 'Local', verified: true, description: '8 hours, 500+ edited photos, album' },
        { seller: 'Dream Weddings Photo', price: 3200, rating: 4.8, location: 'Local', verified: true, description: '10 hours, unlimited photos, video' },
        { seller: 'Capture Love Studios', price: 2800, rating: 4.7, location: 'Local', verified: true, description: '6 hours, 300+ photos, engagement shoot' },
        { seller: 'Perfect Day Photography', price: 3500, rating: 4.9, location: 'Local', verified: true, description: '12 hours, 2 photographers, drone shots' },
        { seller: 'Artistic Visions', price: 2200, rating: 4.6, location: 'Local', verified: false, description: '5 hours, 200+ photos, basic editing' }
      ]
    };

    const offers = baseOffers[scenario] || baseOffers['iPhone 15 Pro'];
    return offers.slice(0, count).map((offer, index) => ({
      ...offer,
      id: index + 1,
      timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      responseTime: Math.floor(Math.random() * 30) + 5 + ' minutes ago'
    }));
  };

  const startDemo = () => {
    const scenario = demoLevels[currentLevel].scenario;
    setUserRequest(scenario);
    simulateOffers();
  };

  const openChat = (offer) => {
    setSelectedOffer(offer);
    setShowChat(true);
    // Initialize chat with welcome message
    setMessages([
      {
        id: 1,
        sender: 'seller',
        text: `Hi! Thanks for your interest in my ${userRequest} offer. I'd be happy to answer any questions!`,
        timestamp: new Date().toISOString()
      }
    ]);
  };

  return (
    <div className="min-h-screen bg-neutral-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-blue/20 to-primary-purple/20" />
      </div>

      {/* Navigation */}
      <nav className="relative z-50 container-custom py-6 border-b border-neutral-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center glow-blue">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <span className="text-2xl font-space-grotesk font-bold text-gradient-primary">
              BestzDealAi
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <a href="/" className="text-neutral-300 hover:text-primary-blue transition-colors">
              ← Back to Home
            </a>
            <Button variant="primary" size="sm" glow>
              Sign Up Free
            </Button>
          </div>
        </div>
      </nav>

      {/* Demo Header */}
      <section className="demo-header relative z-10 container-custom py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-responsive-5xl font-space-grotesk font-bold text-white mb-6">
            Experience the <span className="text-gradient-primary">Future</span> of Marketplace
          </h1>
          <p className="text-responsive-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
            See how BestzDealAi works with our interactive demo. Choose your experience level and watch the magic happen.
          </p>
        </div>
      </section>

      {/* Demo Level Selection */}
      <section className="relative z-10 container-custom py-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Choose Your Demo Experience</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            {Object.entries(demoLevels).map(([level, config]) => (
              <div
                key={level}
                className={cn(
                  "demo-level-card p-6 rounded-xl border cursor-pointer transition-all duration-300",
                  currentLevel === parseInt(level)
                    ? "border-primary-blue bg-gradient-to-br from-neutral-800 to-neutral-900 glow-blue"
                    : "border-neutral-700 bg-neutral-800/50 hover:border-primary-blue/50"
                )}
                onClick={() => setCurrentLevel(parseInt(level))}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white">{config.title}</h3>
                  <span className="text-sm text-neutral-400">{config.duration}</span>
                </div>
                <p className="text-neutral-300 mb-4">{config.description}</p>
                <div className="text-sm text-primary-blue">
                  Scenario: {config.scenario}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Interface */}
      <section className="relative z-10 container-custom py-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Request Form */}
            <div className="lg:col-span-1">
              <div className="bg-neutral-800/50 rounded-xl p-6 border border-neutral-700">
                <h3 className="text-xl font-bold text-white mb-6">Post Your Request</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-300 mb-2">
                      What are you looking for?
                    </label>
                    <input
                      type="text"
                      value={userRequest}
                      onChange={(e) => setUserRequest(e.target.value)}
                      placeholder="e.g., iPhone 15 Pro, Gaming laptop..."
                      className="w-full px-4 py-3 bg-neutral-700 border border-neutral-600 rounded-lg text-white placeholder-neutral-400 focus:border-primary-blue focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-300 mb-2">
                      Category
                    </label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full px-4 py-3 bg-neutral-700 border border-neutral-600 rounded-lg text-white focus:border-primary-blue focus:outline-none"
                    >
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.icon} {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-300 mb-2">
                      Budget Range
                    </label>
                    <input
                      type="text"
                      value={budget}
                      onChange={(e) => setBudget(e.target.value)}
                      placeholder="e.g., $500-1000"
                      className="w-full px-4 py-3 bg-neutral-700 border border-neutral-600 rounded-lg text-white placeholder-neutral-400 focus:border-primary-blue focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-300 mb-2">
                      Location Preference
                    </label>
                    <input
                      type="text"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      placeholder="e.g., New York, NY or Online"
                      className="w-full px-4 py-3 bg-neutral-700 border border-neutral-600 rounded-lg text-white placeholder-neutral-400 focus:border-primary-blue focus:outline-none"
                    />
                  </div>

                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    glow
                    onClick={startDemo}
                    disabled={demoState === 'posting' || demoState === 'receiving'}
                    loading={demoState === 'posting'}
                  >
                    {demoState === 'idle' ? 'Post Request' : 
                     demoState === 'posting' ? 'Posting...' :
                     demoState === 'receiving' ? 'Receiving Offers...' :
                     'Request Posted!'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Offers Display */}
            <div className="lg:col-span-2">
              <div className="bg-neutral-800/50 rounded-xl p-6 border border-neutral-700">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white">
                    Live Offers ({offers.length})
                  </h3>
                  {demoState === 'receiving' && (
                    <div className="flex items-center space-x-2 text-primary-blue">
                      <div className="w-2 h-2 bg-primary-blue rounded-full animate-pulse" />
                      <span className="text-sm">Receiving offers...</span>
                    </div>
                  )}
                </div>

                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {offers.length === 0 && demoState === 'idle' && (
                    <div className="text-center py-12 text-neutral-400">
                      Post a request to see offers appear in real-time
                    </div>
                  )}
                  
                  {offers.map((offer, index) => (
                    <OfferCard
                      key={offer.id}
                      offer={offer}
                      onChat={() => openChat(offer)}
                      animationDelay={index * 0.1}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Chat Modal */}
      {showChat && selectedOffer && (
        <ChatModal
          offer={selectedOffer}
          messages={messages}
          onClose={() => setShowChat(false)}
          onSendMessage={(message) => {
            setMessages(prev => [...prev, {
              id: prev.length + 1,
              sender: 'buyer',
              text: message,
              timestamp: new Date().toISOString()
            }]);
            
            // Simulate seller response
            setTimeout(() => {
              setMessages(prev => [...prev, {
                id: prev.length + 1,
                sender: 'seller',
                text: generateSellerResponse(message),
                timestamp: new Date().toISOString()
              }]);
            }, 1000 + Math.random() * 2000);
          }}
        />
      )}
    </div>
  );
}

// Offer Card Component
function OfferCard({ offer, onChat, animationDelay }) {
  const cardRef = useRef(null);

  useEffect(() => {
    const animateCard = async () => {
      if (typeof window !== 'undefined' && cardRef.current) {
        const { gsap } = await import('gsap');

        gsap.fromTo(cardRef.current,
          { x: 50, opacity: 0 },
          {
            x: 0,
            opacity: 1,
            duration: 0.6,
            delay: animationDelay,
            ease: 'power3.out'
          }
        );
      }
    };

    animateCard();
  }, [animationDelay]);

  return (
    <div
      ref={cardRef}
      className="p-4 bg-neutral-700/50 rounded-lg border border-neutral-600 hover:border-primary-blue/50 transition-all duration-300"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-full flex items-center justify-center text-white font-bold">
            {offer.seller.charAt(0)}
          </div>
          <div>
            <h4 className="text-white font-semibold">{offer.seller}</h4>
            <div className="flex items-center space-x-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <span
                    key={i}
                    className={i < Math.floor(offer.rating) ? 'text-accent-orange' : 'text-neutral-500'}
                  >
                    ⭐
                  </span>
                ))}
              </div>
              <span className="text-sm text-neutral-400">({offer.rating})</span>
              {offer.verified && (
                <span className="text-xs bg-primary-blue text-white px-2 py-1 rounded-full">
                  Verified
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gradient-primary">
            ${offer.price.toLocaleString()}
          </div>
          <div className="text-sm text-neutral-400">{offer.location}</div>
        </div>
      </div>

      <p className="text-neutral-300 text-sm mb-3">{offer.description}</p>

      <div className="flex items-center justify-between">
        <span className="text-xs text-neutral-500">{offer.responseTime}</span>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={onChat}>
            💬 Chat
          </Button>
          <Button variant="primary" size="sm">
            Accept Offer
          </Button>
        </div>
      </div>
    </div>
  );
}

// Chat Modal Component
function ChatModal({ offer, messages, onClose, onSendMessage }) {
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = () => {
    if (newMessage.trim()) {
      onSendMessage(newMessage);
      setNewMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-neutral-800 rounded-xl border border-neutral-700 w-full max-w-2xl h-[600px] flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-neutral-700 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-full flex items-center justify-center text-white font-bold">
              {offer.seller.charAt(0)}
            </div>
            <div>
              <h3 className="text-white font-semibold">{offer.seller}</h3>
              <p className="text-sm text-neutral-400">Offer: ${offer.price.toLocaleString()}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-neutral-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex",
                  message.sender === 'buyer' ? 'justify-end' : 'justify-start'
                )}
              >
                <div
                  className={cn(
                    "max-w-xs lg:max-w-md px-4 py-2 rounded-lg",
                    message.sender === 'buyer'
                      ? 'bg-primary-blue text-white'
                      : 'bg-neutral-700 text-neutral-100'
                  )}
                >
                  <p className="text-sm">{message.text}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Message Input */}
        <div className="p-4 border-t border-neutral-700">
          <div className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="flex-1 px-4 py-2 bg-neutral-700 border border-neutral-600 rounded-lg text-white placeholder-neutral-400 focus:border-primary-blue focus:outline-none"
            />
            <Button variant="primary" onClick={handleSend}>
              Send
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Generate realistic seller responses
function generateSellerResponse(userMessage) {
  const responses = [
    "Thanks for your message! I'd be happy to help with that.",
    "Great question! Let me provide you with more details.",
    "I can definitely work with you on that. What would you prefer?",
    "That's a fair point. I'm flexible on the terms.",
    "I appreciate your interest! I can offer you a special deal.",
    "Absolutely! I have experience with similar requests.",
    "I understand your concerns. Let me address those for you.",
    "Perfect! I think we can make this work for both of us.",
    "I'm confident this will meet your needs. Any other questions?",
    "Thanks for considering my offer! I'm here to help."
  ];

  // Simple keyword-based responses
  const lowerMessage = userMessage.toLowerCase();

  if (lowerMessage.includes('price') || lowerMessage.includes('cost')) {
    return "I'm flexible on pricing. What budget range works best for you?";
  }

  if (lowerMessage.includes('delivery') || lowerMessage.includes('shipping')) {
    return "I can arrange fast delivery. Local pickup is also available if you prefer.";
  }

  if (lowerMessage.includes('warranty') || lowerMessage.includes('guarantee')) {
    return "Yes, this comes with a full warranty and satisfaction guarantee.";
  }

  if (lowerMessage.includes('condition') || lowerMessage.includes('quality')) {
    return "The condition is excellent - I can send additional photos if you'd like.";
  }

  // Return random response if no keywords match
  return responses[Math.floor(Math.random() * responses.length)];
}

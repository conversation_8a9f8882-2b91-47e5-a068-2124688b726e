{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/lib/utils.js"], "sourcesContent": ["import clsx from 'clsx';\n\n/**\n * Utility function to merge class names\n * @param {...(string|object|Array)} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return clsx(inputs);\n}\n\n/**\n * Format currency with proper locale\n * @param {number} amount - Amount to format\n * @param {string} currency - Currency code (default: USD)\n * @param {string} locale - Locale (default: en-US)\n * @returns {string} Formatted currency\n */\nexport function formatCurrency(amount, currency = 'USD', locale = 'en-US') {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\n/**\n * Format number with proper locale\n * @param {number} number - Number to format\n * @param {string} locale - Locale (default: en-US)\n * @returns {string} Formatted number\n */\nexport function formatNumber(number, locale = 'en-US') {\n  return new Intl.NumberFormat(locale).format(number);\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Generate random ID\n * @param {number} length - Length of ID (default: 8)\n * @returns {string} Random ID\n */\nexport function generateId(length = 8) {\n  return Math.random().toString(36).substring(2, length + 2);\n}\n\n/**\n * Check if element is in viewport\n * @param {Element} element - DOM element to check\n * @returns {boolean} Whether element is in viewport\n */\nexport function isInViewport(element) {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  );\n}\n\n/**\n * Smooth scroll to element\n * @param {string|Element} target - Target element or selector\n * @param {number} offset - Offset from top (default: 80)\n */\nexport function scrollToElement(target, offset = 80) {\n  const element = typeof target === 'string' ? document.querySelector(target) : target;\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Get random item from array\n * @param {Array} array - Array to get random item from\n * @returns {*} Random item\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Capitalize first letter of string\n * @param {string} string - String to capitalize\n * @returns {string} Capitalized string\n */\nexport function capitalize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\n/**\n * Truncate string with ellipsis\n * @param {string} string - String to truncate\n * @param {number} length - Maximum length\n * @returns {string} Truncated string\n */\nexport function truncate(string, length) {\n  return string.length > length ? string.substring(0, length) + '...' : string;\n}\n\n/**\n * Sleep function for async operations\n * @param {number} ms - Milliseconds to sleep\n * @returns {Promise} Promise that resolves after specified time\n */\nexport function sleep(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * Check if user prefers reduced motion\n * @returns {boolean} Whether user prefers reduced motion\n */\nexport function prefersReducedMotion() {\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n}\n\n/**\n * Get viewport dimensions\n * @returns {object} Viewport width and height\n */\nexport function getViewportDimensions() {\n  return {\n    width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),\n    height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)\n  };\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} Whether device is mobile\n */\nexport function isMobile() {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * Check if device supports touch\n * @returns {boolean} Whether device supports touch\n */\nexport function isTouchDevice() {\n  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Whether copy was successful\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Format relative time (e.g., \"2 hours ago\")\n * @param {Date|string|number} date - Date to format\n * @returns {string} Relative time string\n */\nexport function formatRelativeTime(date) {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now - targetDate) / 1000);\n\n  if (diffInSeconds < 60) return 'just now';\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} Whether email is valid\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate random color\n * @returns {string} Random hex color\n */\nexport function getRandomColor() {\n  return '#' + Math.floor(Math.random() * 16777215).toString(16);\n}\n\n/**\n * Convert hex to RGB\n * @param {string} hex - Hex color\n * @returns {object} RGB values\n */\nexport function hexToRgb(hex) {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE;AACd;AASO,SAAS,eAAe,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,OAAO;IACvE,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ;QACnC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,aAAa,MAAM,EAAE,SAAS,OAAO;IACnD,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ,MAAM,CAAC;AAC9C;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAOO,SAAS,WAAW,SAAS,CAAC;IACnC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,SAAS;AAC1D;AAOO,SAAS,aAAa,OAAO;IAClC,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAOO,SAAS,gBAAgB,MAAM,EAAE,SAAS,EAAE;IACjD,MAAM,UAAU,OAAO,WAAW,WAAW,SAAS,aAAa,CAAC,UAAU;IAC9E,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,WAAW,MAAM;IAC/B,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD;AAQO,SAAS,SAAS,MAAM,EAAE,MAAM;IACrC,OAAO,OAAO,MAAM,GAAG,SAAS,OAAO,SAAS,CAAC,GAAG,UAAU,QAAQ;AACxE;AAOO,SAAS,MAAM,EAAE;IACtB,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAMO,SAAS;IACd,OAAO,OAAO,UAAU,CAAC,oCAAoC,OAAO;AACtE;AAMO,SAAS;IACd,OAAO;QACL,OAAO,KAAK,GAAG,CAAC,SAAS,eAAe,CAAC,WAAW,IAAI,GAAG,OAAO,UAAU,IAAI;QAChF,QAAQ,KAAK,GAAG,CAAC,SAAS,eAAe,CAAC,YAAY,IAAI,GAAG,OAAO,WAAW,IAAI;IACrF;AACF;AAMO,SAAS;IACd,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAMO,SAAS;IACd,OAAO,kBAAkB,UAAU,UAAU,cAAc,GAAG;AAChE;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAOO,SAAS,mBAAmB,IAAI;IACrC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,UAAU,IAAI;IAEtD,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IACnF,IAAI,gBAAgB,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,SAAS,WAAW,CAAC;IACxF,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,UAAU,UAAU,CAAC;AAC5D;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,OAAO,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC7D;AAOO,SAAS,SAAS,GAAG;IAC1B,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON>lai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Button = forwardRef(({ \n  className, \n  variant = 'primary', \n  size = 'md', \n  children, \n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  glow = false,\n  ...props \n}, ref) => {\n  const baseClasses = [\n    'inline-flex items-center justify-center',\n    'font-medium transition-all duration-300',\n    'focus:outline-none focus:ring-2 focus:ring-offset-2',\n    'disabled:opacity-50 disabled:cursor-not-allowed',\n    'relative overflow-hidden',\n    'border border-transparent',\n  ];\n\n  const variants = {\n    primary: [\n      'bg-gradient-to-r from-primary-blue to-primary-purple',\n      'text-white hover:from-primary-purple hover:to-primary-blue',\n      'focus:ring-primary-blue',\n      glow && 'glow-blue hover:glow-purple',\n    ],\n    secondary: [\n      'bg-neutral-800 border-neutral-700',\n      'text-neutral-100 hover:bg-neutral-700 hover:border-neutral-600',\n      'focus:ring-neutral-500',\n    ],\n    outline: [\n      'border-primary-blue text-primary-blue',\n      'hover:bg-primary-blue hover:text-neutral-900',\n      'focus:ring-primary-blue',\n    ],\n    ghost: [\n      'text-neutral-300 hover:text-primary-blue hover:bg-neutral-800/50',\n      'focus:ring-neutral-500',\n    ],\n    neon: [\n      'bg-gradient-to-r from-accent-neon to-accent-cyan',\n      'text-neutral-900 hover:from-accent-cyan hover:to-accent-neon',\n      'focus:ring-accent-neon',\n      glow && 'glow-neon',\n    ],\n    danger: [\n      'bg-error text-white hover:bg-red-600',\n      'focus:ring-error',\n    ],\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm rounded-md',\n    md: 'px-4 py-2 text-base rounded-lg',\n    lg: 'px-6 py-3 text-lg rounded-xl',\n    xl: 'px-8 py-4 text-xl rounded-2xl',\n  };\n\n  const classes = cn(\n    baseClasses,\n    variants[variant],\n    sizes[size],\n    className\n  );\n\n  const iconClasses = cn(\n    'transition-transform duration-300',\n    size === 'sm' && 'w-4 h-4',\n    size === 'md' && 'w-5 h-5',\n    size === 'lg' && 'w-6 h-6',\n    size === 'xl' && 'w-7 h-7',\n  );\n\n  const LoadingSpinner = () => (\n    <svg \n      className={cn(iconClasses, 'animate-spin')} \n      fill=\"none\" \n      viewBox=\"0 0 24 24\"\n    >\n      <circle \n        className=\"opacity-25\" \n        cx=\"12\" \n        cy=\"12\" \n        r=\"10\" \n        stroke=\"currentColor\" \n        strokeWidth=\"4\"\n      />\n      <path \n        className=\"opacity-75\" \n        fill=\"currentColor\" \n        d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      />\n    </svg>\n  );\n\n  return (\n    <button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {/* Hover effect overlay */}\n      <span className=\"absolute inset-0 bg-white/10 opacity-0 transition-opacity duration-300 hover:opacity-100\" />\n      \n      {/* Content */}\n      <span className=\"relative flex items-center gap-2\">\n        {loading && <LoadingSpinner />}\n        {!loading && icon && iconPosition === 'left' && (\n          <span className={iconClasses}>{icon}</span>\n        )}\n        {children}\n        {!loading && icon && iconPosition === 'right' && (\n          <span className={iconClasses}>{icon}</span>\n        )}\n      </span>\n    </button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACzB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,OAAO,KAAK,EACZ,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;YACP;YACA;YACA;YACA,QAAQ;SACT;QACD,WAAW;YACT;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;YACA,QAAQ;SACT;QACD,QAAQ;YACN;YACA;SACD;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,qCACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ;IAGnB,MAAM,iBAAiB,kBACrB,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAC3B,MAAK;YACL,SAAQ;;8BAER,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;IAKR,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACrB,GAAG,KAAK;;0BAGT,6LAAC;gBAAK,WAAU;;;;;;0BAGhB,6LAAC;gBAAK,WAAU;;oBACb,yBAAW,6LAAC;;;;;oBACZ,CAAC,WAAW,QAAQ,iBAAiB,wBACpC,6LAAC;wBAAK,WAAW;kCAAc;;;;;;oBAEhC;oBACA,CAAC,WAAW,QAAQ,iBAAiB,yBACpC,6LAAC;wBAAK,WAAW;kCAAc;;;;;;;;;;;;;;;;;;AAKzC;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Button from '@/components/ui/Button';\n\nexport default function HomePage() {\n  const heroRef = useRef(null);\n\n  useEffect(() => {\n    // Initialize GSAP animations when component mounts\n    const initAnimations = async () => {\n      if (typeof window !== 'undefined') {\n        const { gsap } = await import('gsap');\n        const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n        gsap.registerPlugin(ScrollTrigger);\n\n        // Hero entrance animation\n        const tl = gsap.timeline();\n        tl.from('.hero-title', {\n          duration: 1,\n          y: 50,\n          opacity: 0,\n          ease: 'power3.out'\n        })\n        .from('.hero-subtitle', {\n          duration: 0.8,\n          y: 30,\n          opacity: 0,\n          ease: 'power3.out'\n        }, '-=0.5')\n        .from('.hero-buttons', {\n          duration: 0.6,\n          y: 20,\n          opacity: 0,\n          ease: 'power3.out'\n        }, '-=0.3');\n      }\n    };\n\n    initAnimations();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-neutral-900 relative overflow-hidden\">\n      {/* Matrix Rain Background */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <MatrixRain />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"relative z-50 container-custom py-6\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center glow-blue\">\n              <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n              </svg>\n            </div>\n            <span className=\"text-2xl font-space-grotesk font-bold text-gradient-primary\">\n              BestzDealAi\n            </span>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Features\n            </a>\n            <a href=\"#demo\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Demo\n            </a>\n            <a href=\"#pricing\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Pricing\n            </a>\n            <a href=\"#about\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              About\n            </a>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"primary\" size=\"sm\" glow>\n              Get Started\n            </Button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section ref={heroRef} className=\"relative z-10 container-custom py-20 lg:py-32\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Hero Title */}\n          <h1 className=\"hero-title text-responsive-6xl font-space-grotesk font-bold mb-6\">\n            <span className=\"text-gradient-primary\">You Post It.</span>\n            <br />\n            <span className=\"text-white\">They Deal It.</span>\n          </h1>\n\n          {/* Hero Subtitle */}\n          <p className=\"hero-subtitle text-responsive-xl text-neutral-300 mb-8 max-w-2xl mx-auto leading-relaxed\">\n            The first AI-powered reverse marketplace where buyers post what they want,\n            and sellers compete to offer the best deal. One post — multiple offers.\n          </p>\n\n          {/* Hero Buttons */}\n          <div className=\"hero-buttons flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Button\n              variant=\"primary\"\n              size=\"lg\"\n              glow\n              className=\"min-w-[200px]\"\n            >\n              Start Dealing Now\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"min-w-[200px]\"\n            >\n              Watch Demo\n            </Button>\n          </div>\n\n          {/* Hero Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-primary mb-2\">87%</div>\n              <div className=\"text-neutral-400\">Better Deals Found</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-accent mb-2\">23min</div>\n              <div className=\"text-neutral-400\">Average Time Saved</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-neon mb-2\">10k+</div>\n              <div className=\"text-neutral-400\">Active Sellers</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Problem Section */}\n      <section className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              The Problem with Traditional Shopping\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              Consumers waste hours comparing prices while sellers struggle to reach buyers.\n              It's time for a smarter approach.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Problem Points */}\n            <div className=\"space-y-8\">\n              <ProblemCard\n                icon=\"⏰\"\n                title=\"Time-Consuming Price Comparison\"\n                description=\"Average 23 minutes spent comparing prices across multiple platforms\"\n              />\n              <ProblemCard\n                icon=\"🔍\"\n                title=\"Limited Local Options\"\n                description=\"68% struggle to find local alternatives and better deals\"\n              />\n              <ProblemCard\n                icon=\"💸\"\n                title=\"High Seller Acquisition Costs\"\n                description=\"Small businesses pay $45-200 per customer acquisition\"\n              />\n              <ProblemCard\n                icon=\"🎯\"\n                title=\"Poor Buyer-Seller Matching\"\n                description=\"89% of small sellers struggle with discoverability\"\n              />\n            </div>\n\n            {/* Solution Visual */}\n            <div className=\"relative\">\n              <div className=\"bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-2xl p-8 glow-blue\">\n                <h3 className=\"text-2xl font-bold text-gradient-primary mb-4\">\n                  Our Solution\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-sm\">1</span>\n                    </div>\n                    <span className=\"text-neutral-200\">Post what you want</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-primary-purple rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-sm\">2</span>\n                    </div>\n                    <span className=\"text-neutral-200\">Sellers compete with offers</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-accent-neon rounded-full flex items-center justify-center\">\n                      <span className=\"text-neutral-900 text-sm\">3</span>\n                    </div>\n                    <span className=\"text-neutral-200\">AI finds the best deal</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n\n// Problem Card Component\nfunction ProblemCard({ icon, title, description }) {\n  return (\n    <div className=\"flex items-start space-x-4 p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue\">\n      <div className=\"text-3xl\">{icon}</div>\n      <div>\n        <h3 className=\"text-xl font-semibold text-white mb-2\">{title}</h3>\n        <p className=\"text-neutral-400\">{description}</p>\n      </div>\n    </div>\n  );\n}\n\n// Matrix Rain Component (Simplified)\nfunction MatrixRain() {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    canvas.width = window.innerWidth;\n    canvas.height = window.innerHeight;\n\n    const matrix = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}\";\n    const matrixArray = matrix.split(\"\");\n\n    const fontSize = 10;\n    const columns = canvas.width / fontSize;\n    const drops = [];\n\n    for (let x = 0; x < columns; x++) {\n      drops[x] = 1;\n    }\n\n    function draw() {\n      ctx.fillStyle = 'rgba(15, 15, 35, 0.04)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      ctx.fillStyle = '#00D4FF';\n      ctx.font = fontSize + 'px monospace';\n\n      for (let i = 0; i < drops.length; i++) {\n        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];\n        ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n\n        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n        drops[i]++;\n      }\n    }\n\n    const interval = setInterval(draw, 35);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return <canvas ref={canvasRef} className=\"absolute inset-0 w-full h-full\" />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,mDAAmD;YACnD,MAAM;qDAAiB;oBACrB,wCAAmC;wBACjC,MAAM,EAAE,IAAI,EAAE,GAAG;wBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;wBAE1B,KAAK,cAAc,CAAC;wBAEpB,0BAA0B;wBAC1B,MAAM,KAAK,KAAK,QAAQ;wBACxB,GAAG,IAAI,CAAC,eAAe;4BACrB,UAAU;4BACV,GAAG;4BACH,SAAS;4BACT,MAAM;wBACR,GACC,IAAI,CAAC,kBAAkB;4BACtB,UAAU;4BACV,GAAG;4BACH,SAAS;4BACT,MAAM;wBACR,GAAG,SACF,IAAI,CAAC,iBAAiB;4BACrB,UAAU;4BACV,GAAG;4BACH,SAAS;4BACT,MAAM;wBACR,GAAG;oBACL;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;;;;;;;;;0BAIH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAe,SAAQ;kDAC9D,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAA8D;;;;;;;;;;;;sCAMhF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAY,WAAU;8CAA6D;;;;;;8CAG3F,6LAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAA6D;;;;;;8CAGvF,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAA6D;;;;;;8CAG1F,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAA6D;;;;;;;;;;;;sCAM1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,IAAI;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC;gBAAQ,KAAK;gBAAS,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;sCAI/B,6LAAC;4BAAE,WAAU;sCAA2F;;;;;;sCAMxG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,IAAI;oCACJ,WAAU;8CACX;;;;;;8CAGD,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgD;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;8CAEpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+C;;;;;;sDAC9D,6LAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;8CAEpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA6C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,6LAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAMvE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAG9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAA2B;;;;;;;;;;;0EAE7C,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD;GApNwB;KAAA;AAsNxB,yBAAyB;AACzB,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAY;;;;;;0BAC3B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAoB;;;;;;;;;;;;;;;;;;AAIzC;MAVS;AAYT,qCAAqC;AACrC,SAAS;;IACP,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;YAElC,MAAM,SAAS;YACf,MAAM,cAAc,OAAO,KAAK,CAAC;YAEjC,MAAM,WAAW;YACjB,MAAM,UAAU,OAAO,KAAK,GAAG;YAC/B,MAAM,QAAQ,EAAE;YAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAChC,KAAK,CAAC,EAAE,GAAG;YACb;YAEA,SAAS;gBACP,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAE9C,IAAI,SAAS,GAAG;gBAChB,IAAI,IAAI,GAAG,WAAW;gBAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,MAAM,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;oBACxE,IAAI,QAAQ,CAAC,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE,GAAG;oBAE5C,IAAI,KAAK,CAAC,EAAE,GAAG,WAAW,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;wBAChE,KAAK,CAAC,EAAE,GAAG;oBACb;oBACA,KAAK,CAAC,EAAE;gBACV;YACF;YAEA,MAAM,WAAW,YAAY,MAAM;YAEnC;wCAAO,IAAM,cAAc;;QAC7B;+BAAG,EAAE;IAEL,qBAAO,6LAAC;QAAO,KAAK;QAAW,WAAU;;;;;;AAC3C;IA9CS;MAAA", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestzdealai/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}]}
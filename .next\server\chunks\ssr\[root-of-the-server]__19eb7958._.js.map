{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/lib/utils.js"], "sourcesContent": ["import clsx from 'clsx';\n\n/**\n * Utility function to merge class names\n * @param {...(string|object|Array)} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return clsx(inputs);\n}\n\n/**\n * Format currency with proper locale\n * @param {number} amount - Amount to format\n * @param {string} currency - Currency code (default: USD)\n * @param {string} locale - Locale (default: en-US)\n * @returns {string} Formatted currency\n */\nexport function formatCurrency(amount, currency = 'USD', locale = 'en-US') {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\n/**\n * Format number with proper locale\n * @param {number} number - Number to format\n * @param {string} locale - Locale (default: en-US)\n * @returns {string} Formatted number\n */\nexport function formatNumber(number, locale = 'en-US') {\n  return new Intl.NumberFormat(locale).format(number);\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Generate random ID\n * @param {number} length - Length of ID (default: 8)\n * @returns {string} Random ID\n */\nexport function generateId(length = 8) {\n  return Math.random().toString(36).substring(2, length + 2);\n}\n\n/**\n * Check if element is in viewport\n * @param {Element} element - DOM element to check\n * @returns {boolean} Whether element is in viewport\n */\nexport function isInViewport(element) {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  );\n}\n\n/**\n * Smooth scroll to element\n * @param {string|Element} target - Target element or selector\n * @param {number} offset - Offset from top (default: 80)\n */\nexport function scrollToElement(target, offset = 80) {\n  const element = typeof target === 'string' ? document.querySelector(target) : target;\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Get random item from array\n * @param {Array} array - Array to get random item from\n * @returns {*} Random item\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Capitalize first letter of string\n * @param {string} string - String to capitalize\n * @returns {string} Capitalized string\n */\nexport function capitalize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\n/**\n * Truncate string with ellipsis\n * @param {string} string - String to truncate\n * @param {number} length - Maximum length\n * @returns {string} Truncated string\n */\nexport function truncate(string, length) {\n  return string.length > length ? string.substring(0, length) + '...' : string;\n}\n\n/**\n * Sleep function for async operations\n * @param {number} ms - Milliseconds to sleep\n * @returns {Promise} Promise that resolves after specified time\n */\nexport function sleep(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * Check if user prefers reduced motion\n * @returns {boolean} Whether user prefers reduced motion\n */\nexport function prefersReducedMotion() {\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n}\n\n/**\n * Get viewport dimensions\n * @returns {object} Viewport width and height\n */\nexport function getViewportDimensions() {\n  return {\n    width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),\n    height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)\n  };\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} Whether device is mobile\n */\nexport function isMobile() {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * Check if device supports touch\n * @returns {boolean} Whether device supports touch\n */\nexport function isTouchDevice() {\n  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Whether copy was successful\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Format relative time (e.g., \"2 hours ago\")\n * @param {Date|string|number} date - Date to format\n * @returns {string} Relative time string\n */\nexport function formatRelativeTime(date) {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now - targetDate) / 1000);\n\n  if (diffInSeconds < 60) return 'just now';\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} Whether email is valid\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Generate random color\n * @returns {string} Random hex color\n */\nexport function getRandomColor() {\n  return '#' + Math.floor(Math.random() * 16777215).toString(16);\n}\n\n/**\n * Convert hex to RGB\n * @param {string} hex - Hex color\n * @returns {object} RGB values\n */\nexport function hexToRgb(hex) {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE;AACd;AASO,SAAS,eAAe,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,OAAO;IACvE,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ;QACnC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAQO,SAAS,aAAa,MAAM,EAAE,SAAS,OAAO;IACnD,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ,MAAM,CAAC;AAC9C;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAOO,SAAS,WAAW,SAAS,CAAC;IACnC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,SAAS;AAC1D;AAOO,SAAS,aAAa,OAAO;IAClC,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAOO,SAAS,gBAAgB,MAAM,EAAE,SAAS,EAAE;IACjD,MAAM,UAAU,OAAO,WAAW,WAAW,SAAS,aAAa,CAAC,UAAU;IAC9E,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,WAAW,MAAM;IAC/B,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD;AAQO,SAAS,SAAS,MAAM,EAAE,MAAM;IACrC,OAAO,OAAO,MAAM,GAAG,SAAS,OAAO,SAAS,CAAC,GAAG,UAAU,QAAQ;AACxE;AAOO,SAAS,MAAM,EAAE;IACtB,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAMO,SAAS;IACd,OAAO,OAAO,UAAU,CAAC,oCAAoC,OAAO;AACtE;AAMO,SAAS;IACd,OAAO;QACL,OAAO,KAAK,GAAG,CAAC,SAAS,eAAe,CAAC,WAAW,IAAI,GAAG,OAAO,UAAU,IAAI;QAChF,QAAQ,KAAK,GAAG,CAAC,SAAS,eAAe,CAAC,YAAY,IAAI,GAAG,OAAO,WAAW,IAAI;IACrF;AACF;AAMO,SAAS;IACd,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAMO,SAAS;IACd,OAAO,kBAAkB,UAAU,UAAU,cAAc,GAAG;AAChE;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAOO,SAAS,mBAAmB,IAAI;IACrC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,UAAU,IAAI;IAEtD,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IACnF,IAAI,gBAAgB,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,SAAS,WAAW,CAAC;IACxF,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,UAAU,UAAU,CAAC;AAC5D;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,OAAO,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC7D;AAOO,SAAS,SAAS,GAAG;IAC1B,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON>lai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Button = forwardRef(({ \n  className, \n  variant = 'primary', \n  size = 'md', \n  children, \n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  glow = false,\n  ...props \n}, ref) => {\n  const baseClasses = [\n    'inline-flex items-center justify-center',\n    'font-medium transition-all duration-300',\n    'focus:outline-none focus:ring-2 focus:ring-offset-2',\n    'disabled:opacity-50 disabled:cursor-not-allowed',\n    'relative overflow-hidden',\n    'border border-transparent',\n  ];\n\n  const variants = {\n    primary: [\n      'bg-gradient-to-r from-primary-blue to-primary-purple',\n      'text-white hover:from-primary-purple hover:to-primary-blue',\n      'focus:ring-primary-blue',\n      glow && 'glow-blue hover:glow-purple',\n    ],\n    secondary: [\n      'bg-neutral-800 border-neutral-700',\n      'text-neutral-100 hover:bg-neutral-700 hover:border-neutral-600',\n      'focus:ring-neutral-500',\n    ],\n    outline: [\n      'border-primary-blue text-primary-blue',\n      'hover:bg-primary-blue hover:text-neutral-900',\n      'focus:ring-primary-blue',\n    ],\n    ghost: [\n      'text-neutral-300 hover:text-primary-blue hover:bg-neutral-800/50',\n      'focus:ring-neutral-500',\n    ],\n    neon: [\n      'bg-gradient-to-r from-accent-neon to-accent-cyan',\n      'text-neutral-900 hover:from-accent-cyan hover:to-accent-neon',\n      'focus:ring-accent-neon',\n      glow && 'glow-neon',\n    ],\n    danger: [\n      'bg-error text-white hover:bg-red-600',\n      'focus:ring-error',\n    ],\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm rounded-md',\n    md: 'px-4 py-2 text-base rounded-lg',\n    lg: 'px-6 py-3 text-lg rounded-xl',\n    xl: 'px-8 py-4 text-xl rounded-2xl',\n  };\n\n  const classes = cn(\n    baseClasses,\n    variants[variant],\n    sizes[size],\n    className\n  );\n\n  const iconClasses = cn(\n    'transition-transform duration-300',\n    size === 'sm' && 'w-4 h-4',\n    size === 'md' && 'w-5 h-5',\n    size === 'lg' && 'w-6 h-6',\n    size === 'xl' && 'w-7 h-7',\n  );\n\n  const LoadingSpinner = () => (\n    <svg \n      className={cn(iconClasses, 'animate-spin')} \n      fill=\"none\" \n      viewBox=\"0 0 24 24\"\n    >\n      <circle \n        className=\"opacity-25\" \n        cx=\"12\" \n        cy=\"12\" \n        r=\"10\" \n        stroke=\"currentColor\" \n        strokeWidth=\"4\"\n      />\n      <path \n        className=\"opacity-75\" \n        fill=\"currentColor\" \n        d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      />\n    </svg>\n  );\n\n  return (\n    <button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {/* Hover effect overlay */}\n      <span className=\"absolute inset-0 bg-white/10 opacity-0 transition-opacity duration-300 hover:opacity-100\" />\n      \n      {/* Content */}\n      <span className=\"relative flex items-center gap-2\">\n        {loading && <LoadingSpinner />}\n        {!loading && icon && iconPosition === 'left' && (\n          <span className={iconClasses}>{icon}</span>\n        )}\n        {children}\n        {!loading && icon && iconPosition === 'right' && (\n          <span className={iconClasses}>{icon}</span>\n        )}\n      </span>\n    </button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,OAAO,KAAK,EACZ,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;YACP;YACA;YACA;YACA,QAAQ;SACT;QACD,WAAW;YACT;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;YACA,QAAQ;SACT;QACD,QAAQ;YACN;YACA;SACD;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,qCACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ;IAGnB,MAAM,iBAAiB,kBACrB,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAC3B,MAAK;YACL,SAAQ;;8BAER,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;IAKR,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACrB,GAAG,KAAK;;0BAGT,8OAAC;gBAAK,WAAU;;;;;;0BAGhB,8OAAC;gBAAK,WAAU;;oBACb,yBAAW,8OAAC;;;;;oBACZ,CAAC,WAAW,QAAQ,iBAAiB,wBACpC,8OAAC;wBAAK,WAAW;kCAAc;;;;;;oBAEhC;oBACA,CAAC,WAAW,QAAQ,iBAAiB,yBACpC,8OAAC;wBAAK,WAAW;kCAAc;;;;;;;;;;;;;;;;;;AAKzC;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Button from '@/components/ui/Button';\n\nexport default function HomePage() {\n  const heroRef = useRef(null);\n\n  useEffect(() => {\n    // Initialize GSAP animations when component mounts\n    const initAnimations = async () => {\n      if (typeof window !== 'undefined') {\n        const { gsap } = await import('gsap');\n        const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n        gsap.registerPlugin(ScrollTrigger);\n\n        // Hero entrance animation\n        const tl = gsap.timeline();\n        tl.from('.hero-title', {\n          duration: 1,\n          y: 50,\n          opacity: 0,\n          ease: 'power3.out'\n        })\n        .from('.hero-subtitle', {\n          duration: 0.8,\n          y: 30,\n          opacity: 0,\n          ease: 'power3.out'\n        }, '-=0.5')\n        .from('.hero-buttons', {\n          duration: 0.6,\n          y: 20,\n          opacity: 0,\n          ease: 'power3.out'\n        }, '-=0.3');\n      }\n    };\n\n    initAnimations();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-neutral-900 relative overflow-hidden\">\n      {/* Matrix Rain Background */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <MatrixRain />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"relative z-50 container-custom py-6\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center glow-blue\">\n              <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n              </svg>\n            </div>\n            <span className=\"text-2xl font-space-grotesk font-bold text-gradient-primary\">\n              BestzDealAi\n            </span>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Features\n            </a>\n            <a href=\"#demo\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Demo\n            </a>\n            <a href=\"#pricing\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              Pricing\n            </a>\n            <a href=\"#about\" className=\"text-neutral-300 hover:text-primary-blue transition-colors\">\n              About\n            </a>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"primary\" size=\"sm\" glow>\n              Get Started\n            </Button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section ref={heroRef} className=\"relative z-10 container-custom py-20 lg:py-32\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Hero Title */}\n          <h1 className=\"hero-title text-responsive-6xl font-space-grotesk font-bold mb-6\">\n            <span className=\"text-gradient-primary\">You Post It.</span>\n            <br />\n            <span className=\"text-white\">They Deal It.</span>\n          </h1>\n\n          {/* Hero Subtitle */}\n          <p className=\"hero-subtitle text-responsive-xl text-neutral-300 mb-8 max-w-2xl mx-auto leading-relaxed\">\n            The first AI-powered reverse marketplace where buyers post what they want,\n            and sellers compete to offer the best deal. One post — multiple offers.\n          </p>\n\n          {/* Hero Buttons */}\n          <div className=\"hero-buttons flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Button\n              variant=\"primary\"\n              size=\"lg\"\n              glow\n              className=\"min-w-[200px]\"\n            >\n              Start Dealing Now\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"min-w-[200px]\"\n            >\n              Watch Demo\n            </Button>\n          </div>\n\n          {/* Hero Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-primary mb-2\">87%</div>\n              <div className=\"text-neutral-400\">Better Deals Found</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-accent mb-2\">23min</div>\n              <div className=\"text-neutral-400\">Average Time Saved</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient-neon mb-2\">10k+</div>\n              <div className=\"text-neutral-400\">Active Sellers</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Problem Section */}\n      <section className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              The Problem with Traditional Shopping\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              Consumers waste hours comparing prices while sellers struggle to reach buyers.\n              It's time for a smarter approach.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Problem Points */}\n            <div className=\"space-y-8\">\n              <ProblemCard\n                icon=\"⏰\"\n                title=\"Time-Consuming Price Comparison\"\n                description=\"Average 23 minutes spent comparing prices across multiple platforms\"\n              />\n              <ProblemCard\n                icon=\"🔍\"\n                title=\"Limited Local Options\"\n                description=\"68% struggle to find local alternatives and better deals\"\n              />\n              <ProblemCard\n                icon=\"💸\"\n                title=\"High Seller Acquisition Costs\"\n                description=\"Small businesses pay $45-200 per customer acquisition\"\n              />\n              <ProblemCard\n                icon=\"🎯\"\n                title=\"Poor Buyer-Seller Matching\"\n                description=\"89% of small sellers struggle with discoverability\"\n              />\n            </div>\n\n            {/* Solution Visual */}\n            <div className=\"relative\">\n              <div className=\"bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-2xl p-8 glow-blue\">\n                <h3 className=\"text-2xl font-bold text-gradient-primary mb-4\">\n                  Our Solution\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-sm\">1</span>\n                    </div>\n                    <span className=\"text-neutral-200\">Post what you want</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-primary-purple rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-sm\">2</span>\n                    </div>\n                    <span className=\"text-neutral-200\">Sellers compete with offers</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-accent-neon rounded-full flex items-center justify-center\">\n                      <span className=\"text-neutral-900 text-sm\">3</span>\n                    </div>\n                    <span className=\"text-neutral-200\">AI finds the best deal</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              MVP Features That Change Everything\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              Experience the future of marketplace commerce with AI-powered deal matching and real-time seller competition.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <FeatureCard\n              icon=\"🤖\"\n              title=\"AI Deal Matching\"\n              description=\"Smart algorithms analyze offers and match you with the best deals based on value, not just price.\"\n              gradient=\"from-primary-blue to-primary-purple\"\n            />\n            <FeatureCard\n              icon=\"⚡\"\n              title=\"Real-Time Offers\"\n              description=\"Get multiple competitive offers within minutes of posting your request.\"\n              gradient=\"from-accent-cyan to-accent-pink\"\n            />\n            <FeatureCard\n              icon=\"🎯\"\n              title=\"Local & Online Sellers\"\n              description=\"Access both local businesses and online sellers in one unified platform.\"\n              gradient=\"from-accent-neon to-primary-green\"\n            />\n            <FeatureCard\n              icon=\"💬\"\n              title=\"Smart Negotiation\"\n              description=\"Built-in chat system with AI-assisted negotiation suggestions.\"\n              gradient=\"from-primary-purple to-accent-pink\"\n            />\n            <FeatureCard\n              icon=\"🔒\"\n              title=\"Secure Transactions\"\n              description=\"End-to-end encryption and secure payment processing for peace of mind.\"\n              gradient=\"from-primary-green to-accent-cyan\"\n            />\n            <FeatureCard\n              icon=\"📊\"\n              title=\"Deal Analytics\"\n              description=\"Track savings, compare offers, and get insights on your purchasing patterns.\"\n              gradient=\"from-accent-orange to-error\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Competitor Comparison Section */}\n      <section className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              Why BestzDealAi Beats Traditional Marketplaces\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              See how we stack up against the competition and why sellers and buyers choose us.\n            </p>\n          </div>\n\n          <div className=\"bg-neutral-800/50 rounded-2xl p-8 border border-neutral-700\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-neutral-600\">\n                    <th className=\"text-left py-4 px-6 text-neutral-300\">Feature</th>\n                    <th className=\"text-center py-4 px-6 text-gradient-primary font-bold\">BestzDealAi</th>\n                    <th className=\"text-center py-4 px-6 text-neutral-400\">Amazon</th>\n                    <th className=\"text-center py-4 px-6 text-neutral-400\">eBay</th>\n                    <th className=\"text-center py-4 px-6 text-neutral-400\">Facebook</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <ComparisonRow\n                    feature=\"Buyer-Initiated Requests\"\n                    bestzdeal=\"✅\"\n                    amazon=\"❌\"\n                    ebay=\"❌\"\n                    facebook=\"❌\"\n                  />\n                  <ComparisonRow\n                    feature=\"AI-Powered Matching\"\n                    bestzdeal=\"✅\"\n                    amazon=\"Partial\"\n                    ebay=\"❌\"\n                    facebook=\"❌\"\n                  />\n                  <ComparisonRow\n                    feature=\"Local Seller Priority\"\n                    bestzdeal=\"✅\"\n                    amazon=\"❌\"\n                    ebay=\"Partial\"\n                    facebook=\"✅\"\n                  />\n                  <ComparisonRow\n                    feature=\"Real-Time Negotiation\"\n                    bestzdeal=\"✅\"\n                    amazon=\"❌\"\n                    ebay=\"Partial\"\n                    facebook=\"Basic\"\n                  />\n                  <ComparisonRow\n                    feature=\"Low Seller Fees\"\n                    bestzdeal=\"2.5%\"\n                    amazon=\"8-15%\"\n                    ebay=\"10-13%\"\n                    facebook=\"5%\"\n                  />\n                  <ComparisonRow\n                    feature=\"Free for Buyers\"\n                    bestzdeal=\"✅\"\n                    amazon=\"✅\"\n                    ebay=\"✅\"\n                    facebook=\"✅\"\n                  />\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              What Early Users Are Saying\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              Join thousands of satisfied buyers and sellers who've discovered a better way to deal.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <TestimonialCard\n              name=\"Sarah Chen\"\n              role=\"Small Business Owner\"\n              avatar=\"👩‍💼\"\n              rating={5}\n              text=\"Finally, a platform where customers come to me! I've increased my sales by 40% since joining BestzDealAi.\"\n            />\n            <TestimonialCard\n              name=\"Mike Rodriguez\"\n              role=\"Deal Hunter\"\n              avatar=\"🛍️\"\n              rating={5}\n              text=\"Saved over $2,000 last month alone. The AI really finds deals I never would have discovered.\"\n            />\n            <TestimonialCard\n              name=\"Lisa Park\"\n              role=\"Local Retailer\"\n              avatar=\"🏪\"\n              rating={5}\n              text=\"Best customer acquisition tool I've ever used. Quality leads, real buyers, fair pricing.\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Pricing Section */}\n      <section id=\"pricing\" className=\"relative z-10 container-custom py-20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-responsive-4xl font-space-grotesk font-bold text-white mb-6\">\n              Simple, Transparent Pricing\n            </h2>\n            <p className=\"text-responsive-lg text-neutral-300 max-w-3xl mx-auto\">\n              Start free and scale as you grow. No hidden fees, no surprises.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <PricingCard\n              title=\"Buyer\"\n              price=\"Free\"\n              period=\"Forever\"\n              description=\"Perfect for deal seekers\"\n              features={[\n                \"Unlimited deal requests\",\n                \"AI-powered offer matching\",\n                \"Real-time notifications\",\n                \"Basic chat support\",\n                \"Deal comparison tools\"\n              ]}\n              buttonText=\"Start Dealing\"\n              popular={false}\n            />\n            <PricingCard\n              title=\"Seller Basic\"\n              price=\"$19\"\n              period=\"per month\"\n              description=\"Great for small businesses\"\n              features={[\n                \"Up to 50 offers/month\",\n                \"Basic analytics\",\n                \"Standard support\",\n                \"Profile customization\",\n                \"Payment processing\"\n              ]}\n              buttonText=\"Start Selling\"\n              popular={true}\n            />\n            <PricingCard\n              title=\"Seller Pro\"\n              price=\"$49\"\n              period=\"per month\"\n              description=\"For growing businesses\"\n              features={[\n                \"Unlimited offers\",\n                \"Advanced analytics\",\n                \"Priority support\",\n                \"AI offer optimization\",\n                \"Custom branding\",\n                \"API access\"\n              ]}\n              buttonText=\"Go Pro\"\n              popular={false}\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"relative z-10 bg-neutral-950 border-t border-neutral-800\">\n        <div className=\"container-custom py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* Logo and Description */}\n            <div className=\"md:col-span-2\">\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n                  </svg>\n                </div>\n                <span className=\"text-xl font-space-grotesk font-bold text-gradient-primary\">\n                  BestzDealAi\n                </span>\n              </div>\n              <p className=\"text-neutral-400 mb-4 max-w-md\">\n                The AI-powered reverse marketplace where buyers post what they want,\n                and sellers compete to offer the best deal.\n              </p>\n              <p className=\"text-sm text-neutral-500\">\n                © 2025 BestzDealAi. All rights reserved.\n              </p>\n            </div>\n\n            {/* Quick Links */}\n            <div>\n              <h3 className=\"text-white font-semibold mb-4\">Quick Links</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"#features\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Features</a></li>\n                <li><a href=\"#demo\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Demo</a></li>\n                <li><a href=\"#pricing\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Pricing</a></li>\n                <li><a href=\"#about\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">About</a></li>\n              </ul>\n            </div>\n\n            {/* Contact */}\n            <div>\n              <h3 className=\"text-white font-semibold mb-4\">Contact</h3>\n              <ul className=\"space-y-2\">\n                <li><a href=\"mailto:<EMAIL>\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\"><EMAIL></a></li>\n                <li><a href=\"#\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Support Center</a></li>\n                <li><a href=\"#\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Privacy Policy</a></li>\n                <li><a href=\"#\" className=\"text-neutral-400 hover:text-primary-blue transition-colors\">Terms of Service</a></li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n// Problem Card Component\nfunction ProblemCard({ icon, title, description }) {\n  return (\n    <div className=\"flex items-start space-x-4 p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue\">\n      <div className=\"text-3xl\">{icon}</div>\n      <div>\n        <h3 className=\"text-xl font-semibold text-white mb-2\">{title}</h3>\n        <p className=\"text-neutral-400\">{description}</p>\n      </div>\n    </div>\n  );\n}\n\n// Matrix Rain Component (Simplified)\nfunction MatrixRain() {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    canvas.width = window.innerWidth;\n    canvas.height = window.innerHeight;\n\n    const matrix = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}\";\n    const matrixArray = matrix.split(\"\");\n\n    const fontSize = 10;\n    const columns = canvas.width / fontSize;\n    const drops = [];\n\n    for (let x = 0; x < columns; x++) {\n      drops[x] = 1;\n    }\n\n    function draw() {\n      ctx.fillStyle = 'rgba(15, 15, 35, 0.04)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      ctx.fillStyle = '#00D4FF';\n      ctx.font = fontSize + 'px monospace';\n\n      for (let i = 0; i < drops.length; i++) {\n        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];\n        ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n\n        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n        drops[i]++;\n      }\n    }\n\n    const interval = setInterval(draw, 35);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return <canvas ref={canvasRef} className=\"absolute inset-0 w-full h-full\" />;\n}\n\n// Feature Card Component\nfunction FeatureCard({ icon, title, description, gradient }) {\n  return (\n    <div className=\"group relative p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue\">\n      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300`} />\n      <div className=\"relative\">\n        <div className=\"text-4xl mb-4\">{icon}</div>\n        <h3 className=\"text-xl font-semibold text-white mb-3\">{title}</h3>\n        <p className=\"text-neutral-400 leading-relaxed\">{description}</p>\n      </div>\n    </div>\n  );\n}\n\n// Comparison Row Component\nfunction ComparisonRow({ feature, bestzdeal, amazon, ebay, facebook }) {\n  return (\n    <tr className=\"border-b border-neutral-700/50\">\n      <td className=\"py-4 px-6 text-neutral-200\">{feature}</td>\n      <td className=\"py-4 px-6 text-center text-primary-blue font-semibold\">{bestzdeal}</td>\n      <td className=\"py-4 px-6 text-center text-neutral-400\">{amazon}</td>\n      <td className=\"py-4 px-6 text-center text-neutral-400\">{ebay}</td>\n      <td className=\"py-4 px-6 text-center text-neutral-400\">{facebook}</td>\n    </tr>\n  );\n}\n\n// Testimonial Card Component\nfunction TestimonialCard({ name, role, avatar, rating, text }) {\n  return (\n    <div className=\"p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue\">\n      <div className=\"flex items-center mb-4\">\n        <div className=\"text-3xl mr-3\">{avatar}</div>\n        <div>\n          <h4 className=\"text-white font-semibold\">{name}</h4>\n          <p className=\"text-neutral-400 text-sm\">{role}</p>\n        </div>\n      </div>\n      <div className=\"flex mb-3\">\n        {[...Array(rating)].map((_, i) => (\n          <span key={i} className=\"text-accent-orange\">⭐</span>\n        ))}\n      </div>\n      <p className=\"text-neutral-300 italic\">\"{text}\"</p>\n    </div>\n  );\n}\n\n// Pricing Card Component\nfunction PricingCard({ title, price, period, description, features, buttonText, popular }) {\n  return (\n    <div className={`relative p-8 rounded-2xl border transition-all duration-300 ${\n      popular\n        ? 'border-primary-blue bg-gradient-to-br from-neutral-800 to-neutral-900 glow-blue'\n        : 'border-neutral-700 bg-neutral-800/50 hover:border-primary-blue/50'\n    }`}>\n      {popular && (\n        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n          <span className=\"bg-gradient-to-r from-primary-blue to-primary-purple text-white px-4 py-1 rounded-full text-sm font-semibold\">\n            Most Popular\n          </span>\n        </div>\n      )}\n\n      <div className=\"text-center mb-6\">\n        <h3 className=\"text-2xl font-bold text-white mb-2\">{title}</h3>\n        <div className=\"mb-2\">\n          <span className=\"text-4xl font-bold text-gradient-primary\">{price}</span>\n          {period && <span className=\"text-neutral-400 ml-2\">/{period}</span>}\n        </div>\n        <p className=\"text-neutral-400\">{description}</p>\n      </div>\n\n      <ul className=\"space-y-3 mb-8\">\n        {features.map((feature, index) => (\n          <li key={index} className=\"flex items-center text-neutral-300\">\n            <span className=\"text-primary-blue mr-3\">✓</span>\n            {feature}\n          </li>\n        ))}\n      </ul>\n\n      <Button\n        variant={popular ? \"primary\" : \"outline\"}\n        size=\"lg\"\n        className=\"w-full\"\n        glow={popular}\n      >\n        {buttonText}\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,MAAM,iBAAiB;YACrB,uCAAmC;;YA0BnC;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;;;;;;;;;0BAIH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAe,SAAQ;kDAC9D,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAA8D;;;;;;;;;;;;sCAMhF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAA6D;;;;;;8CAG3F,8OAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAA6D;;;;;;8CAGvF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAA6D;;;;;;8CAG1F,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAA6D;;;;;;;;;;;;sCAM1F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,IAAI;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAQ,KAAK;gBAAS,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;sCAI/B,8OAAC;4BAAE,WAAU;sCAA2F;;;;;;sCAMxG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,IAAI;oCACJ,WAAU;8CACX;;;;;;8CAGD,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgD;;;;;;sDAC/D,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+C;;;;;;sDAC9D,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA6C;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAMvE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAG9D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAA2B;;;;;;;;;;;0EAE7C,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAKvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;8CAEX,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;8CAEX,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;8CAEX,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;8CAEX,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;8CAEX,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,UAAS;;;;;;;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAKvE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAG,WAAU;kEAAwD;;;;;;kEACtE,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;;;;;;;;;;;;sDAG3D,8OAAC;;8DACC,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;8DAEX,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;8DAEX,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;8DAEX,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;8DAEX,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;8DAEX,8OAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,QAAO;oDACP,MAAK;oDACL,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAKvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,QAAO;oCACP,QAAQ;oCACR,MAAK;;;;;;8CAEP,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,QAAO;oCACP,QAAQ;oCACR,MAAK;;;;;;8CAEP,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,QAAO;oCACP,QAAQ;oCACR,MAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAwD;;;;;;;;;;;;sCAKvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,aAAY;oCACZ,UAAU;wCACR;wCACA;wCACA;wCACA;wCACA;qCACD;oCACD,YAAW;oCACX,SAAS;;;;;;8CAEX,8OAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,aAAY;oCACZ,UAAU;wCACR;wCACA;wCACA;wCACA;wCACA;qCACD;oCACD,YAAW;oCACX,SAAS;;;;;;8CAEX,8OAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,aAAY;oCACZ,UAAU;wCACR;wCACA;wCACA;wCACA;wCACA;wCACA;qCACD;oCACD,YAAW;oCACX,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAe,SAAQ;8DAC9D,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAK,WAAU;0DAA6D;;;;;;;;;;;;kDAI/E,8OAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAI9C,8OAAC;wCAAE,WAAU;kDAA2B;;;;;;;;;;;;0CAM1C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAY,WAAU;8DAA6D;;;;;;;;;;;0DAC/F,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAQ,WAAU;8DAA6D;;;;;;;;;;;0DAC3F,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAW,WAAU;8DAA6D;;;;;;;;;;;0DAC9F,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAS,WAAU;8DAA6D;;;;;;;;;;;;;;;;;;;;;;;0CAKhG,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAA+B,WAAU;8DAA6D;;;;;;;;;;;0DAClH,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAA6D;;;;;;;;;;;0DACvF,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAA6D;;;;;;;;;;;0DACvF,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvG;AAEA,yBAAyB;AACzB,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAY;;;;;;0BAC3B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAoB;;;;;;;;;;;;;;;;;;AAIzC;AAEA,qCAAqC;AACrC,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,OAAO,KAAK,GAAG,OAAO,UAAU;QAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QAElC,MAAM,SAAS;QACf,MAAM,cAAc,OAAO,KAAK,CAAC;QAEjC,MAAM,WAAW;QACjB,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,QAAQ,EAAE;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAChC,KAAK,CAAC,EAAE,GAAG;QACb;QAEA,SAAS;YACP,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE9C,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI,GAAG,WAAW;YAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;gBACxE,IAAI,QAAQ,CAAC,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE,GAAG;gBAE5C,IAAI,KAAK,CAAC,EAAE,GAAG,WAAW,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;oBAChE,KAAK,CAAC,EAAE,GAAG;gBACb;gBACA,KAAK,CAAC,EAAE;YACV;QACF;QAEA,MAAM,WAAW,YAAY,MAAM;QAEnC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBAAO,8OAAC;QAAO,KAAK;QAAW,WAAU;;;;;;AAC3C;AAEA,yBAAyB;AACzB,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE;IACzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAC,mCAAmC,EAAE,SAAS,4EAA4E,CAAC;;;;;;0BAC5I,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiB;;;;;;kCAChC,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAoC;;;;;;;;;;;;;;;;;;AAIzD;AAEA,2BAA2B;AAC3B,SAAS,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;IACnE,qBACE,8OAAC;QAAG,WAAU;;0BACZ,8OAAC;gBAAG,WAAU;0BAA8B;;;;;;0BAC5C,8OAAC;gBAAG,WAAU;0BAAyD;;;;;;0BACvE,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;;;;;;;AAG9D;AAEA,6BAA6B;AAC7B,SAAS,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE;IAC3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiB;;;;;;kCAChC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;0BAG7C,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC;wBAAa,WAAU;kCAAqB;uBAAlC;;;;;;;;;;0BAGf,8OAAC;gBAAE,WAAU;;oBAA0B;oBAAE;oBAAK;;;;;;;;;;;;;AAGpD;AAEA,yBAAyB;AACzB,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE;IACvF,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,UACI,oFACA,qEACJ;;YACC,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAA+G;;;;;;;;;;;0BAMnI,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;4BAC3D,wBAAU,8OAAC;gCAAK,WAAU;;oCAAwB;oCAAE;;;;;;;;;;;;;kCAEvD,8OAAC;wBAAE,WAAU;kCAAoB;;;;;;;;;;;;0BAGnC,8OAAC;gBAAG,WAAU;0BACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wBAAe,WAAU;;0CACxB,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;4BACxC;;uBAFM;;;;;;;;;;0BAOb,8OAAC,iIAAA,CAAA,UAAM;gBACL,SAAS,UAAU,YAAY;gBAC/B,MAAK;gBACL,WAAU;gBACV,MAAM;0BAEL;;;;;;;;;;;;AAIT", "debugId": null}}]}
'use client';

import { forwardRef } from 'react';
import { cn } from '@/lib/utils';

const Button = forwardRef(({ 
  className, 
  variant = 'primary', 
  size = 'md', 
  children, 
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  glow = false,
  ...props 
}, ref) => {
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium transition-all duration-300',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'relative overflow-hidden',
    'border border-transparent',
  ];

  const variants = {
    primary: [
      'bg-gradient-to-r from-primary-blue to-primary-purple',
      'text-white hover:from-primary-purple hover:to-primary-blue',
      'focus:ring-primary-blue',
      glow && 'glow-blue hover:glow-purple',
    ],
    secondary: [
      'bg-neutral-800 border-neutral-700',
      'text-neutral-100 hover:bg-neutral-700 hover:border-neutral-600',
      'focus:ring-neutral-500',
    ],
    outline: [
      'border-primary-blue text-primary-blue',
      'hover:bg-primary-blue hover:text-neutral-900',
      'focus:ring-primary-blue',
    ],
    ghost: [
      'text-neutral-300 hover:text-primary-blue hover:bg-neutral-800/50',
      'focus:ring-neutral-500',
    ],
    neon: [
      'bg-gradient-to-r from-accent-neon to-accent-cyan',
      'text-neutral-900 hover:from-accent-cyan hover:to-accent-neon',
      'focus:ring-accent-neon',
      glow && 'glow-neon',
    ],
    danger: [
      'bg-error text-white hover:bg-red-600',
      'focus:ring-error',
    ],
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl',
    xl: 'px-8 py-4 text-xl rounded-2xl',
  };

  const classes = cn(
    baseClasses,
    variants[variant],
    sizes[size],
    className
  );

  const iconClasses = cn(
    'transition-transform duration-300',
    size === 'sm' && 'w-4 h-4',
    size === 'md' && 'w-5 h-5',
    size === 'lg' && 'w-6 h-6',
    size === 'xl' && 'w-7 h-7',
  );

  const LoadingSpinner = () => (
    <svg 
      className={cn(iconClasses, 'animate-spin')} 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  return (
    <button
      ref={ref}
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {/* Hover effect overlay */}
      <span className="absolute inset-0 bg-white/10 opacity-0 transition-opacity duration-300 hover:opacity-100" />
      
      {/* Content */}
      <span className="relative flex items-center gap-2">
        {loading && <LoadingSpinner />}
        {!loading && icon && iconPosition === 'left' && (
          <span className={iconClasses}>{icon}</span>
        )}
        {children}
        {!loading && icon && iconPosition === 'right' && (
          <span className={iconClasses}>{icon}</span>
        )}
      </span>
    </button>
  );
});

Button.displayName = 'Button';

export default Button;

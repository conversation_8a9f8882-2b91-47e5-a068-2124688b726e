'use client';

import { useEffect, useRef } from 'react';
import Button from '@/components/ui/Button';

export default function HomePage() {
  const heroRef = useRef(null);

  useEffect(() => {
    // Initialize GSAP animations when component mounts
    const initAnimations = async () => {
      if (typeof window !== 'undefined') {
        const { gsap } = await import('gsap');
        const { ScrollTrigger } = await import('gsap/ScrollTrigger');

        gsap.registerPlugin(ScrollTrigger);

        // Hero entrance animation
        const tl = gsap.timeline();
        tl.from('.hero-title', {
          duration: 1,
          y: 50,
          opacity: 0,
          ease: 'power3.out'
        })
        .from('.hero-subtitle', {
          duration: 0.8,
          y: 30,
          opacity: 0,
          ease: 'power3.out'
        }, '-=0.5')
        .from('.hero-buttons', {
          duration: 0.6,
          y: 20,
          opacity: 0,
          ease: 'power3.out'
        }, '-=0.3');
      }
    };

    initAnimations();
  }, []);

  return (
    <div className="min-h-screen bg-neutral-900 relative overflow-hidden">
      {/* Matrix Rain Background */}
      <div className="absolute inset-0 opacity-20">
        <MatrixRain />
      </div>

      {/* Navigation */}
      <nav className="relative z-50 container-custom py-6">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center glow-blue">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <span className="text-2xl font-space-grotesk font-bold text-gradient-primary">
              BestzDealAi
            </span>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-neutral-300 hover:text-primary-blue transition-colors">
              Features
            </a>
            <a href="#demo" className="text-neutral-300 hover:text-primary-blue transition-colors">
              Demo
            </a>
            <a href="#pricing" className="text-neutral-300 hover:text-primary-blue transition-colors">
              Pricing
            </a>
            <a href="#about" className="text-neutral-300 hover:text-primary-blue transition-colors">
              About
            </a>
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              Sign In
            </Button>
            <Button variant="primary" size="sm" glow>
              Get Started
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className="relative z-10 container-custom py-20 lg:py-32">
        <div className="max-w-4xl mx-auto text-center">
          {/* Hero Title */}
          <h1 className="hero-title text-responsive-6xl font-space-grotesk font-bold mb-6">
            <span className="text-gradient-primary">You Post It.</span>
            <br />
            <span className="text-white">They Deal It.</span>
          </h1>

          {/* Hero Subtitle */}
          <p className="hero-subtitle text-responsive-xl text-neutral-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            The first AI-powered reverse marketplace where buyers post what they want,
            and sellers compete to offer the best deal. One post — multiple offers.
          </p>

          {/* Hero Buttons */}
          <div className="hero-buttons flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button
              variant="primary"
              size="lg"
              glow
              className="min-w-[200px]"
            >
              Start Dealing Now
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="min-w-[200px]"
            >
              Watch Demo
            </Button>
          </div>

          {/* Hero Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient-primary mb-2">87%</div>
              <div className="text-neutral-400">Better Deals Found</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient-accent mb-2">23min</div>
              <div className="text-neutral-400">Average Time Saved</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient-neon mb-2">10k+</div>
              <div className="text-neutral-400">Active Sellers</div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="relative z-10 container-custom py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-responsive-4xl font-space-grotesk font-bold text-white mb-6">
              The Problem with Traditional Shopping
            </h2>
            <p className="text-responsive-lg text-neutral-300 max-w-3xl mx-auto">
              Consumers waste hours comparing prices while sellers struggle to reach buyers.
              It's time for a smarter approach.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Problem Points */}
            <div className="space-y-8">
              <ProblemCard
                icon="⏰"
                title="Time-Consuming Price Comparison"
                description="Average 23 minutes spent comparing prices across multiple platforms"
              />
              <ProblemCard
                icon="🔍"
                title="Limited Local Options"
                description="68% struggle to find local alternatives and better deals"
              />
              <ProblemCard
                icon="💸"
                title="High Seller Acquisition Costs"
                description="Small businesses pay $45-200 per customer acquisition"
              />
              <ProblemCard
                icon="🎯"
                title="Poor Buyer-Seller Matching"
                description="89% of small sellers struggle with discoverability"
              />
            </div>

            {/* Solution Visual */}
            <div className="relative">
              <div className="bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-2xl p-8 glow-blue">
                <h3 className="text-2xl font-bold text-gradient-primary mb-4">
                  Our Solution
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">1</span>
                    </div>
                    <span className="text-neutral-200">Post what you want</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary-purple rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">2</span>
                    </div>
                    <span className="text-neutral-200">Sellers compete with offers</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-accent-neon rounded-full flex items-center justify-center">
                      <span className="text-neutral-900 text-sm">3</span>
                    </div>
                    <span className="text-neutral-200">AI finds the best deal</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative z-10 container-custom py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-responsive-4xl font-space-grotesk font-bold text-white mb-6">
              MVP Features That Change Everything
            </h2>
            <p className="text-responsive-lg text-neutral-300 max-w-3xl mx-auto">
              Experience the future of marketplace commerce with AI-powered deal matching and real-time seller competition.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon="🤖"
              title="AI Deal Matching"
              description="Smart algorithms analyze offers and match you with the best deals based on value, not just price."
              gradient="from-primary-blue to-primary-purple"
            />
            <FeatureCard
              icon="⚡"
              title="Real-Time Offers"
              description="Get multiple competitive offers within minutes of posting your request."
              gradient="from-accent-cyan to-accent-pink"
            />
            <FeatureCard
              icon="🎯"
              title="Local & Online Sellers"
              description="Access both local businesses and online sellers in one unified platform."
              gradient="from-accent-neon to-primary-green"
            />
            <FeatureCard
              icon="💬"
              title="Smart Negotiation"
              description="Built-in chat system with AI-assisted negotiation suggestions."
              gradient="from-primary-purple to-accent-pink"
            />
            <FeatureCard
              icon="🔒"
              title="Secure Transactions"
              description="End-to-end encryption and secure payment processing for peace of mind."
              gradient="from-primary-green to-accent-cyan"
            />
            <FeatureCard
              icon="📊"
              title="Deal Analytics"
              description="Track savings, compare offers, and get insights on your purchasing patterns."
              gradient="from-accent-orange to-error"
            />
          </div>
        </div>
      </section>

      {/* Competitor Comparison Section */}
      <section className="relative z-10 container-custom py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-responsive-4xl font-space-grotesk font-bold text-white mb-6">
              Why BestzDealAi Beats Traditional Marketplaces
            </h2>
            <p className="text-responsive-lg text-neutral-300 max-w-3xl mx-auto">
              See how we stack up against the competition and why sellers and buyers choose us.
            </p>
          </div>

          <div className="bg-neutral-800/50 rounded-2xl p-8 border border-neutral-700">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-neutral-600">
                    <th className="text-left py-4 px-6 text-neutral-300">Feature</th>
                    <th className="text-center py-4 px-6 text-gradient-primary font-bold">BestzDealAi</th>
                    <th className="text-center py-4 px-6 text-neutral-400">Amazon</th>
                    <th className="text-center py-4 px-6 text-neutral-400">eBay</th>
                    <th className="text-center py-4 px-6 text-neutral-400">Facebook</th>
                  </tr>
                </thead>
                <tbody>
                  <ComparisonRow
                    feature="Buyer-Initiated Requests"
                    bestzdeal="✅"
                    amazon="❌"
                    ebay="❌"
                    facebook="❌"
                  />
                  <ComparisonRow
                    feature="AI-Powered Matching"
                    bestzdeal="✅"
                    amazon="Partial"
                    ebay="❌"
                    facebook="❌"
                  />
                  <ComparisonRow
                    feature="Local Seller Priority"
                    bestzdeal="✅"
                    amazon="❌"
                    ebay="Partial"
                    facebook="✅"
                  />
                  <ComparisonRow
                    feature="Real-Time Negotiation"
                    bestzdeal="✅"
                    amazon="❌"
                    ebay="Partial"
                    facebook="Basic"
                  />
                  <ComparisonRow
                    feature="Low Seller Fees"
                    bestzdeal="2.5%"
                    amazon="8-15%"
                    ebay="10-13%"
                    facebook="5%"
                  />
                  <ComparisonRow
                    feature="Free for Buyers"
                    bestzdeal="✅"
                    amazon="✅"
                    ebay="✅"
                    facebook="✅"
                  />
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="relative z-10 container-custom py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-responsive-4xl font-space-grotesk font-bold text-white mb-6">
              What Early Users Are Saying
            </h2>
            <p className="text-responsive-lg text-neutral-300 max-w-3xl mx-auto">
              Join thousands of satisfied buyers and sellers who've discovered a better way to deal.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <TestimonialCard
              name="Sarah Chen"
              role="Small Business Owner"
              avatar="👩‍💼"
              rating={5}
              text="Finally, a platform where customers come to me! I've increased my sales by 40% since joining BestzDealAi."
            />
            <TestimonialCard
              name="Mike Rodriguez"
              role="Deal Hunter"
              avatar="🛍️"
              rating={5}
              text="Saved over $2,000 last month alone. The AI really finds deals I never would have discovered."
            />
            <TestimonialCard
              name="Lisa Park"
              role="Local Retailer"
              avatar="🏪"
              rating={5}
              text="Best customer acquisition tool I've ever used. Quality leads, real buyers, fair pricing."
            />
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="relative z-10 container-custom py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-responsive-4xl font-space-grotesk font-bold text-white mb-6">
              Simple, Transparent Pricing
            </h2>
            <p className="text-responsive-lg text-neutral-300 max-w-3xl mx-auto">
              Start free and scale as you grow. No hidden fees, no surprises.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <PricingCard
              title="Buyer"
              price="Free"
              period="Forever"
              description="Perfect for deal seekers"
              features={[
                "Unlimited deal requests",
                "AI-powered offer matching",
                "Real-time notifications",
                "Basic chat support",
                "Deal comparison tools"
              ]}
              buttonText="Start Dealing"
              popular={false}
            />
            <PricingCard
              title="Seller Basic"
              price="$19"
              period="per month"
              description="Great for small businesses"
              features={[
                "Up to 50 offers/month",
                "Basic analytics",
                "Standard support",
                "Profile customization",
                "Payment processing"
              ]}
              buttonText="Start Selling"
              popular={true}
            />
            <PricingCard
              title="Seller Pro"
              price="$49"
              period="per month"
              description="For growing businesses"
              features={[
                "Unlimited offers",
                "Advanced analytics",
                "Priority support",
                "AI offer optimization",
                "Custom branding",
                "API access"
              ]}
              buttonText="Go Pro"
              popular={false}
            />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-neutral-950 border-t border-neutral-800">
        <div className="container-custom py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Logo and Description */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-blue to-primary-purple rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                  </svg>
                </div>
                <span className="text-xl font-space-grotesk font-bold text-gradient-primary">
                  BestzDealAi
                </span>
              </div>
              <p className="text-neutral-400 mb-4 max-w-md">
                The AI-powered reverse marketplace where buyers post what they want,
                and sellers compete to offer the best deal.
              </p>
              <p className="text-sm text-neutral-500">
                © 2025 BestzDealAi. All rights reserved.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><a href="#features" className="text-neutral-400 hover:text-primary-blue transition-colors">Features</a></li>
                <li><a href="#demo" className="text-neutral-400 hover:text-primary-blue transition-colors">Demo</a></li>
                <li><a href="#pricing" className="text-neutral-400 hover:text-primary-blue transition-colors">Pricing</a></li>
                <li><a href="#about" className="text-neutral-400 hover:text-primary-blue transition-colors">About</a></li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h3 className="text-white font-semibold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li><a href="mailto:<EMAIL>" className="text-neutral-400 hover:text-primary-blue transition-colors"><EMAIL></a></li>
                <li><a href="#" className="text-neutral-400 hover:text-primary-blue transition-colors">Support Center</a></li>
                <li><a href="#" className="text-neutral-400 hover:text-primary-blue transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-neutral-400 hover:text-primary-blue transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Problem Card Component
function ProblemCard({ icon, title, description }) {
  return (
    <div className="flex items-start space-x-4 p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue">
      <div className="text-3xl">{icon}</div>
      <div>
        <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
        <p className="text-neutral-400">{description}</p>
      </div>
    </div>
  );
}

// Matrix Rain Component (Simplified)
function MatrixRain() {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
    const matrixArray = matrix.split("");

    const fontSize = 10;
    const columns = canvas.width / fontSize;
    const drops = [];

    for (let x = 0; x < columns; x++) {
      drops[x] = 1;
    }

    function draw() {
      ctx.fillStyle = 'rgba(15, 15, 35, 0.04)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = '#00D4FF';
      ctx.font = fontSize + 'px monospace';

      for (let i = 0; i < drops.length; i++) {
        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
        ctx.fillText(text, i * fontSize, drops[i] * fontSize);

        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
        drops[i]++;
      }
    }

    const interval = setInterval(draw, 35);

    return () => clearInterval(interval);
  }, []);

  return <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />;
}

// Feature Card Component
function FeatureCard({ icon, title, description, gradient }) {
  return (
    <div className="group relative p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue">
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300`} />
      <div className="relative">
        <div className="text-4xl mb-4">{icon}</div>
        <h3 className="text-xl font-semibold text-white mb-3">{title}</h3>
        <p className="text-neutral-400 leading-relaxed">{description}</p>
      </div>
    </div>
  );
}

// Comparison Row Component
function ComparisonRow({ feature, bestzdeal, amazon, ebay, facebook }) {
  return (
    <tr className="border-b border-neutral-700/50">
      <td className="py-4 px-6 text-neutral-200">{feature}</td>
      <td className="py-4 px-6 text-center text-primary-blue font-semibold">{bestzdeal}</td>
      <td className="py-4 px-6 text-center text-neutral-400">{amazon}</td>
      <td className="py-4 px-6 text-center text-neutral-400">{ebay}</td>
      <td className="py-4 px-6 text-center text-neutral-400">{facebook}</td>
    </tr>
  );
}

// Testimonial Card Component
function TestimonialCard({ name, role, avatar, rating, text }) {
  return (
    <div className="p-6 bg-neutral-800/50 rounded-xl border border-neutral-700 hover:border-primary-blue/50 transition-all duration-300 hover:glow-blue">
      <div className="flex items-center mb-4">
        <div className="text-3xl mr-3">{avatar}</div>
        <div>
          <h4 className="text-white font-semibold">{name}</h4>
          <p className="text-neutral-400 text-sm">{role}</p>
        </div>
      </div>
      <div className="flex mb-3">
        {[...Array(rating)].map((_, i) => (
          <span key={i} className="text-accent-orange">⭐</span>
        ))}
      </div>
      <p className="text-neutral-300 italic">"{text}"</p>
    </div>
  );
}

// Pricing Card Component
function PricingCard({ title, price, period, description, features, buttonText, popular }) {
  return (
    <div className={`relative p-8 rounded-2xl border transition-all duration-300 ${
      popular
        ? 'border-primary-blue bg-gradient-to-br from-neutral-800 to-neutral-900 glow-blue'
        : 'border-neutral-700 bg-neutral-800/50 hover:border-primary-blue/50'
    }`}>
      {popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-4 py-1 rounded-full text-sm font-semibold">
            Most Popular
          </span>
        </div>
      )}

      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-white mb-2">{title}</h3>
        <div className="mb-2">
          <span className="text-4xl font-bold text-gradient-primary">{price}</span>
          {period && <span className="text-neutral-400 ml-2">/{period}</span>}
        </div>
        <p className="text-neutral-400">{description}</p>
      </div>

      <ul className="space-y-3 mb-8">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center text-neutral-300">
            <span className="text-primary-blue mr-3">✓</span>
            {feature}
          </li>
        ))}
      </ul>

      <Button
        variant={popular ? "primary" : "outline"}
        size="lg"
        className="w-full"
        glow={popular}
      >
        {buttonText}
      </Button>
    </div>
  );
}

/* [next]/internal/font/google/space_grotesk_a8d4bd84.module.css [app-client] (css) */
@font-face {
  font-family: Space Grotesk;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/V8mDoQDjQSkFtoMM3T6r8E7mPb54C_k3HqUtEw-s.ec93cae9.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Space Grotesk;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/V8mDoQDjQSkFtoMM3T6r8E7mPb94C_k3HqUtEw-s.bb80b367.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Space Grotesk;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/V8mDoQDjQSkFtoMM3T6r8E7mPbF4C_k3HqU-s.p.e4176d6d.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Space Grotesk Fallback;
  src: local(Arial);
  ascent-override: 89.71%;
  descent-override: 26.62%;
  line-gap-override: 0.0%;
  size-adjust: 109.69%;
}

.space_grotesk_a8d4bd84-module__qNEJ0G__className {
  font-family: Space Grotesk, Space Grotesk Fallback;
  font-style: normal;
}

.space_grotesk_a8d4bd84-module__qNEJ0G__variable {
  --font-space-grotesk: "Space Grotesk", "Space Grotesk Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_space_grotesk_a8d4bd84_module_css_f9ee138c._.single.css.map*/
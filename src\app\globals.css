@import "tailwindcss";

/* BestzDealAi Futuristic Design System */
:root {
  /* Primary Colors - AI/Futuristic Theme */
  --primary-blue: #00D4FF;
  --primary-purple: #8B5CF6;
  --primary-green: #10B981;

  /* Accent Colors */
  --accent-cyan: #06B6D4;
  --accent-pink: #EC4899;
  --accent-orange: #F59E0B;
  --accent-neon: #39FF14;

  /* Neutral Colors - Dark Theme */
  --neutral-950: #030712;
  --neutral-900: #0F0F23;
  --neutral-800: #1A1A2E;
  --neutral-700: #16213E;
  --neutral-600: #0F3460;
  --neutral-500: #533483;
  --neutral-400: #7C3AED;
  --neutral-300: #A78BFA;
  --neutral-200: #C4B5FD;
  --neutral-100: #EDE9FE;
  --neutral-50: #F5F3FF;

  /* Semantic Colors */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-cyan) 0%, var(--accent-pink) 100%);
  --gradient-neon: linear-gradient(135deg, var(--accent-neon) 0%, var(--primary-blue) 100%);

  /* Shadows */
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-neon: 0 0 30px rgba(57, 255, 20, 0.4);
  --shadow-purple: 0 0 25px rgba(139, 92, 246, 0.3);

  /* Animation Durations */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.6s;
  --duration-slower: 1s;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

@theme inline {
  /* Font Families */
  --font-inter: var(--font-inter);
  --font-space-grotesk: var(--font-space-grotesk);
  --font-jetbrains-mono: var(--font-jetbrains-mono);

  /* Colors */
  --color-primary-blue: var(--primary-blue);
  --color-primary-purple: var(--primary-purple);
  --color-primary-green: var(--primary-green);
  --color-accent-cyan: var(--accent-cyan);
  --color-accent-pink: var(--accent-pink);
  --color-accent-orange: var(--accent-orange);
  --color-accent-neon: var(--accent-neon);

  --color-neutral-950: var(--neutral-950);
  --color-neutral-900: var(--neutral-900);
  --color-neutral-800: var(--neutral-800);
  --color-neutral-700: var(--neutral-700);
  --color-neutral-600: var(--neutral-600);
  --color-neutral-500: var(--neutral-500);
  --color-neutral-400: var(--neutral-400);
  --color-neutral-300: var(--neutral-300);
  --color-neutral-200: var(--neutral-200);
  --color-neutral-100: var(--neutral-100);
  --color-neutral-50: var(--neutral-50);

  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  background: var(--neutral-900);
  color: var(--neutral-100);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Selection Styles */
::selection {
  background: var(--primary-blue);
  color: var(--neutral-900);
}

::-moz-selection {
  background: var(--primary-blue);
  color: var(--neutral-900);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-800);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-cyan);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Utility Classes */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-neon {
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-blue {
  box-shadow: var(--shadow-glow);
}

.glow-neon {
  box-shadow: var(--shadow-neon);
}

.glow-purple {
  box-shadow: var(--shadow-purple);
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-matrix-text {
  animation: matrix-text 0.1s linear infinite;
}

/* Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.6);
  }
}

@keyframes matrix-text {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive Typography */
.text-responsive-xs {
  font-size: clamp(0.75rem, 2vw, 0.875rem);
}

.text-responsive-sm {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 3vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 3.5vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 2rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 6vw, 2.5rem);
}

.text-responsive-4xl {
  font-size: clamp(2.25rem, 7vw, 3rem);
}

.text-responsive-5xl {
  font-size: clamp(3rem, 8vw, 4rem);
}

.text-responsive-6xl {
  font-size: clamp(3.75rem, 10vw, 5rem);
}

/* Container Utilities */
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 3rem;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

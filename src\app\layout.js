import { Inter, Space_Grotesk, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "BestzDealAi - AI-Powered Reverse Marketplace",
  description: "Post what you want, get multiple competitive offers. AI-powered reverse marketplace where sellers compete for your business. You post it. They deal it.",
  keywords: "reverse marketplace, AI deals, competitive offers, price comparison, local sellers, online marketplace",
  authors: [{ name: "BestzDealAi Team" }],
  creator: "BestzDealAi",
  publisher: "BestzDealAi",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://bestzdealai.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "BestzDealAi - AI-Powered Reverse Marketplace",
    description: "Post what you want, get multiple competitive offers. AI-powered reverse marketplace where sellers compete for your business.",
    url: "https://bestzdealai.com",
    siteName: "BestzDealAi",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "BestzDealAi - AI-Powered Reverse Marketplace",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "BestzDealAi - AI-Powered Reverse Marketplace",
    description: "Post what you want, get multiple competitive offers. AI-powered reverse marketplace where sellers compete for your business.",
    images: ["/twitter-image.jpg"],
    creator: "@bestzdealai",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${spaceGrotesk.variable} ${jetbrainsMono.variable} antialiased bg-neutral-900 text-neutral-100 font-inter overflow-x-hidden`}
      >
        {children}
      </body>
    </html>
  );
}
